# Guide de Navigation - Visualiseur NDE

## 🔍 Comprendre vos données

Votre fichier `test.nde` contient **2 datasets différents** :

### Dataset 1: "Amplitude" (uint16)
- **Type**: Données 3D
- **Shape**: (1050, 55, 640)
- **Contenu**: 1050 slices d'images ultrasonores de 55x640 pixels
- **Navigation**: ✅ Possible avec ← → ou A/D (1050 slices)

### Dataset 2: "Status" (uint8) 
- **Type**: Données 2D
- **Shape**: (1050, 55)
- **Contenu**: 1 seule image de masque de statut
- **Navigation**: ❌ Pas de slices à naviguer

## 🎮 Navigation Correcte

### 1. Navigation entre DATASETS
```
↑ ↓ (ou W/S) : Changer de dataset
```
- Utilisez ces touches pour passer d'un dataset à l'autre
- Dataset 0 → Dataset 1 → Dataset 0...

### 2. Navigation entre SLICES (3D uniquement)
```
← → (ou A/D) : Changer de slice
```
- **Fonctionne SEULEMENT** sur le dataset "Amplitude" (3D)
- **Ne fonctionne PAS** sur le dataset "Status" (2D)

## ✅ Comportement Attendu

### Quand vous êtes sur "Amplitude" (uint16, 3D):
- ← → : Change les slices (1/1050 → 2/1050 → ... → 1050/1050)
- Le titre affiche : `"Amplitude - Slice X/1050 (3D)"`
- Messages : `"➡️ Slice 2/1050"` ou `"⬅️ Slice 1/1050"`

### Quand vous êtes sur "Status" (uint8, 2D):
- ← → : Affiche le message `"ℹ️ Dataset 'Status' est 2D - pas de slices à naviguer"`
- Le titre affiche : `"Status (2D - pas de slices)"`
- **C'est normal !** Il n'y a qu'une seule image.

## 🚀 Instructions d'Utilisation

### Via l'interface graphique (recommandé):
```bash
python interface_graphique.py
```
1. Sélectionner "🔬 Visualiser fichiers NDE"
2. Choisir votre fichier .nde
3. Cliquer "Exécuter"

### En ligne de commande:
```bash
python view_nde_files_stable.py nde/test.nde
```

## 🎯 Séquence de Test Recommandée

1. **Démarrer le visualiseur**
2. **Vérifier le dataset initial** : Vous devriez voir "Amplitude" (3D)
3. **Tester la navigation de slices** : ← → pour naviguer entre les 1050 slices
4. **Changer de dataset** : ↑ ou ↓ pour aller sur "Status"
5. **Vérifier le comportement 2D** : ← → affiche le message informatif
6. **Retourner au dataset 3D** : ↑ ou ↓ pour revenir sur "Amplitude"

## 💡 Messages Informatifs

Le visualiseur affiche maintenant des messages clairs :

- `"➡️ Slice 5/1050"` : Navigation réussie
- `"ℹ️ Dataset 'Status' est 2D - pas de slices à naviguer"` : Normal pour les datasets 2D
- `"ℹ️ Déjà à la première slice (1/1050)"` : Vous êtes au début
- `"ℹ️ Déjà à la dernière slice (1050/1050)"` : Vous êtes à la fin

## ❓ FAQ

**Q: Pourquoi les slices ne changent pas sur le dataset uint8 ?**
R: C'est normal ! Le dataset uint8 "Status" est 2D (une seule image). Seul le dataset uint16 "Amplitude" est 3D avec 1050 slices.

**Q: Comment savoir si un dataset a des slices ?**
R: Regardez le titre de la fenêtre :
- `"(3D)"` = A des slices
- `"(2D - pas de slices)"` = Pas de slices

**Q: Comment passer d'un dataset à l'autre ?**
R: Utilisez ↑ ↓ (ou W/S) ou les boutons "Dataset ↑/↓"

## 🎉 Résumé

**Le visualiseur fonctionne correctement !** 

- Dataset uint16 "Amplitude" : 1050 slices navigables ✅
- Dataset uint8 "Status" : 1 image fixe ✅
- Messages informatifs clairs ✅
- Navigation intuitive ✅

Le fait que les slices ne changent pas sur le dataset uint8 est le **comportement attendu et correct** car ce dataset ne contient qu'une seule image 2D.
