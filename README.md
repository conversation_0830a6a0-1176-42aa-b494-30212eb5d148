# 🔬 Outils de Traitement et Visualisation d'Images Médicales

Ce projet contient une collection d'outils pour le traitement et la visualisation d'images médicales, principalement pour les formats NIfTI et PNG. Il inclut des interfaces graphiques pour faciliter l'utilisation des scripts.

## 📋 Table des Matières

- [Installation](#installation)
- [Interfaces Graphiques](#interfaces-graphiques)
- [Scripts de Traitement](#scripts-de-traitement)
- [Scripts de Visualisation](#scripts-de-visualisation)
- [Scripts Utilitaires](#scripts-utilitaires)
- [Utilisation](#utilisation)

## 🛠️ Installation

### Prérequis
- Python 3.7+
- Les bibliothèques suivantes :
  ```bash
  pip install nibabel opencv-python numpy matplotlib tkinter
  ```

### Dépendances principales
- `nibabel` : Lecture/écriture de fichiers NIfTI
- `opencv-python` : Traitement d'images PNG
- `numpy` : Calculs numériques
- `matplotlib` : Visualisation
- `tkinter` : Interface graphique (inclus avec Python)

## 🖥️ Interfaces Graphiques

### `interface_graphique.py`
Interface graphique complète avec toutes les fonctionnalités :
- Sélection de scripts via menu déroulant
- Configuration des paramètres avec sélecteurs de fichiers/dossiers
- Affichage en temps réel de la sortie des scripts
- Support de tous les scripts disponibles

**Utilisation :**
```bash
python interface_graphique.py
```

### `interface_simple.py`
Interface graphique simplifiée pour les opérations de base :
- Version allégée avec fonctionnalités essentielles
- Scripts intégrés pour les opérations courantes
- Interface plus rapide et légère

**Utilisation :**
```bash
python interface_simple.py
```

## 📦 Scripts de Traitement

### `convertir_dataset_2d_to_3d.py`
Convertit un dataset d'images 2D en volumes 3D pour l'entraînement nnU-Net.

**Fonctionnalités :**
- Regroupe les slices 2D en volumes 3D
- Génère les fichiers de configuration nnU-Net
- Traite les images et les labels simultanément

**Paramètres configurables :**
- Dossier des images d'entrée
- Dossier des labels
- Dossier de sortie
- Nom du dataset
- Nombre de slices par volume

### `convertir_inference_2d_to_3d.py`
Convertit les résultats d'inférence 2D en volumes 3D.

**Fonctionnalités :**
- Regroupe les prédictions 2D en volumes 3D
- Maintient la cohérence spatiale
- Format compatible avec l'évaluation

**Paramètres configurables :**
- Dossier des images d'inférence
- Dossier de sortie
- Nombre de slices par volume

## 👁️ Scripts de Visualisation

### `view_nifti_volume.py`
Visualiseur interactif pour volumes NIfTI 3D.

**Fonctionnalités :**
- Navigation par slices avec clavier
- Détection automatique : masque vs intensité
- Colormap adaptative selon le type de données
- Sauvegarde de slices individuelles
- Affichage d'informations détaillées

**Contrôles :**
- `←/→` : Slice précédente/suivante
- `Page Up/Down` : ±10 slices
- `Home/End` : Première/dernière slice
- `S` : Sauvegarder la slice actuelle
- `I` : Informations sur la slice
- `Q/Escape` : Quitter

### `view_png_folder.py`
Visualiseur interactif pour dossiers d'images PNG.

**Fonctionnalités :**
- Navigation dans un dossier d'images
- Détection automatique : masque vs intensité
- Limitation optionnelle du nombre d'images
- Mêmes contrôles que le visualiseur NIfTI

**Paramètres :**
- Dossier d'images PNG
- Nombre maximum d'images (optionnel)

### `visualize_mask.py`
Visualiseur spécialisé pour masques avec colormap personnalisée.

**Fonctionnalités :**
- Colormap spécifique pour valeurs 1,2,3,4
- Affichage optimisé pour les masques de segmentation

### `visualize_labels.py`
Analyseur de labels pour datasets de segmentation.

**Fonctionnalités :**
- Analyse des valeurs uniques dans chaque image
- Statistiques sur la distribution des labels
- Détection d'anomalies dans les labels

## 🔧 Scripts Utilitaires

### `check_shape.py`
Vérificateur de dimensions pour fichiers NIfTI.

**Fonctionnalités :**
- Affichage des dimensions du volume
- Vérification de l'intégrité du fichier
- Informations sur le format

### `transpose_nifti.py`
Transposition d'axes pour volumes NIfTI.

**Fonctionnalités :**
- Réorganisation des axes (x,y,z) → (y,z,x)
- Préservation des métadonnées
- Utile pour la compatibilité entre formats

## 🚀 Utilisation

### Via Interface Graphique (Recommandé)
1. Lancez `interface_graphique.py`
2. Sélectionnez le script désiré dans le menu déroulant
3. Configurez les paramètres via les sélecteurs
4. Cliquez sur "Exécuter"
5. Suivez la progression dans la zone de sortie

### Via Ligne de Commande
Chaque script peut être exécuté directement :
```bash
python nom_du_script.py
```

### Exemples d'Usage

**Visualiser un volume NIfTI :**
```bash
python view_nifti_volume.py
# Puis sélectionner le fichier via l'interface
```

**Analyser des labels :**
```bash
python visualize_labels.py
# Puis sélectionner le dossier de labels
```

**Convertir un dataset 2D vers 3D :**
1. Utiliser l'interface graphique
2. Sélectionner "Convertir dataset 2D vers 3D"
3. Configurer les dossiers et paramètres
4. Exécuter

## 📝 Notes Importantes

- **Formats supportés :** NIfTI (.nii.gz), PNG
- **Encodage :** UTF-8 pour tous les fichiers
- **Compatibilité :** Windows, Linux, macOS
- **Performance :** Les visualiseurs sont optimisés pour de gros volumes
- **Mémoire :** Chargement progressif pour économiser la RAM

## 🔍 Dépannage

**Erreur d'encodage :** Vérifiez que vos chemins ne contiennent pas de caractères spéciaux
**Fichier non trouvé :** Utilisez les sélecteurs de fichiers de l'interface graphique
**Mémoire insuffisante :** Limitez le nombre d'images chargées simultanément
**Visualiseur ne s'ouvre pas :** Vérifiez l'installation de matplotlib et tkinter

## 👨‍💻 Auteur

Gabriel Forest - 2025-06-18

---

*Ce projet fait partie des outils de traitement d'images pour la détection de corrosion.*
