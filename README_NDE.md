# Visualiseur de fichiers NDE

## Nouveautés ajoutées

### 🔬 Script de visualisation NDE (`view_nde_files.py`)

Un nouveau script pour visualiser les fichiers `.nde` (qui sont en fait des fichiers HDF5 contenant des arrays d'images).

**Fonctionnalités :**
- Détection automatique des datasets d'images dans le fichier
- Navigation interactive entre les slices et datasets
- Affichage optimisé pour les images uint8/uint16 (données brutes)
- Contrôles clavier intuitifs

**Contrôles disponibles :**
- `← →` (ou `A/D`) : Slice précédente/suivante
- `↑ ↓` (ou `W/S`) : Dataset précédent/suivant  
- `Home/End` : Première/dernière slice
- `I` : Informations détaillées
- `H` : Afficher l'aide
- `Q/Escape` : Quitter

### 📋 Intégration dans l'interface graphique

Le script NDE a été ajouté à `interface_graphique.py` :
- Nouvelle option : "🔬 Visualiser fichiers NDE (view_nde_files.py)"
- Sélecteur de fichiers avec filtres pour `.nde`, `.h5`
- Exécution intégrée avec affichage des instructions

### 🗑️ Nettoyage

- Suppression de `interface_simple.py` (redondant avec `interface_graphique.py`)
- Conservation uniquement de l'interface graphique plus complète

## Structure des fichiers NDE analysés

Le fichier `nde/test.nde` contient :

1. **Amplitude** : Dataset 3D (1050, 55, 640) en uint16
   - Données ultrasonores brutes d'amplitude
   - 640 slices de 1050x55 pixels

2. **Status** : Dataset 2D (1050, 55) en uint8  
   - Masque de statut/qualité
   - Image unique de 1050x55 pixels

## Utilisation

### Via l'interface graphique (recommandé)
```bash
python interface_graphique.py
```
1. Sélectionner "🔬 Visualiser fichiers NDE (view_nde_files.py)"
2. Choisir le fichier `.nde`
3. Cliquer sur "Exécuter"

### En ligne de commande
```bash
python view_nde_files.py nde/test.nde
```

### Test d'intégration
```bash
python test_nde_integration.py
```

## Dépendances

Le script nécessite :
- `h5py` : Pour lire les fichiers HDF5/NDE
- `matplotlib` : Pour l'affichage interactif
- `numpy` : Pour le traitement des arrays

Installation :
```bash
pip install h5py matplotlib numpy
```

## Notes techniques

- Les fichiers `.nde` sont des fichiers HDF5 standard
- Le script privilégie les données uint8/uint16 (images brutes) comme demandé
- Navigation optimisée pour les gros datasets (cache intelligent)
- Gestion robuste des erreurs et des formats non supportés
