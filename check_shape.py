import nibabel as nib

def check_nifti_shape(file_path):
    try:
        # Charger le fichier NIfTI
        nifti_img = nib.load(file_path)
        
        # Obtenir la forme de l'image
        shape = nifti_img.shape
        
        print(f"Chemin du fichier: {file_path}")
        print(f"Forme de l'image: {shape}")
        print(f"Nombre de dimensions: {len(shape)}")
        
    except Exception as e:
        print(f"Erreur lors de la lecture du fichier: {str(e)}")

if __name__ == "__main__":
    # Chemin hardcodé vers le fichier
    file_path = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldiferent_transposed_backup\case_001_0000_transposed.nii.gz"
    check_nifti_shape(file_path) 