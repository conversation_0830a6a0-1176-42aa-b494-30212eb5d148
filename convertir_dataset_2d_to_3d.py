"""
Script simple pour convertir des datasets 2D (images PNG organisées en dossiers) 
vers des volumes 3D NIfTI compatibles avec nnU-Net.

Auteur: Gabriel Forest
Date: 2025-06-16
"""

import os
import numpy as np
import nibabel as nib
from PIL import Image
from pathlib import Path
from natsort import natsorted
import json


def convert_png_to_nifti_3d(images_root, labels_root, output_dataset_dir,
                           dataset_name="Dataset013_test4labeldifferentUint8_3d",
                           file_ending=".nii.gz", slices_per_volume=10):
    """
    Convertit des images PNG 2D individuelles en volumes 3D NIfTI.
    Groupe les images par paquets pour créer des volumes 3D.

    Args:
        images_root: Chemin vers le dossier contenant les images PNG
        labels_root: Chemin vers le dossier contenant les labels PNG
        output_dataset_dir: Dossier de sortie pour le dataset nnU-Net
        dataset_name: Nom du dataset
        file_ending: Extension des fichiers NIfTI
        slices_per_volume: Nombre de slices par volume 3D
    """
    # Création de la structure de sortie
    output_dir = Path(output_dataset_dir) / dataset_name
    imagesTr_dir = output_dir / "imagesTr"
    labelsTr_dir = output_dir / "labelsTr"
    imagesTr_dir.mkdir(parents=True, exist_ok=True)
    labelsTr_dir.mkdir(parents=True, exist_ok=True)

    # Récupération des fichiers PNG
    img_files = natsorted([f for f in os.listdir(images_root) if f.endswith('.png')])
    lbl_files = natsorted([f for f in os.listdir(labels_root) if f.endswith('.png')])

    print(f"📂 Trouvé {len(img_files)} images et {len(lbl_files)} labels")

    if len(img_files) != len(lbl_files):
        print(f"⚠️ ATTENTION: Nombre d'images ({len(img_files)}) != nombre de labels ({len(lbl_files)})")
        # Prendre le minimum pour éviter les erreurs
        min_files = min(len(img_files), len(lbl_files))
        img_files = img_files[:min_files]
        lbl_files = lbl_files[:min_files]
        print(f"🔧 Utilisation de {min_files} fichiers")

    # Grouper les images en volumes
    num_volumes = len(img_files) // slices_per_volume
    if len(img_files) % slices_per_volume != 0:
        num_volumes += 1  # Volume partiel pour les images restantes

    print(f"🔄 Création de {num_volumes} volumes 3D ({slices_per_volume} slices par volume)...")

    for vol_idx in range(num_volumes):
        start_idx = vol_idx * slices_per_volume
        end_idx = min(start_idx + slices_per_volume, len(img_files))

        volume_img = []
        volume_lbl = []

        # Charger les slices pour ce volume
        for i in range(start_idx, end_idx):
            img_path = Path(images_root) / img_files[i]
            lbl_path = Path(labels_root) / lbl_files[i]

            img = np.array(Image.open(img_path).convert("L"))  # grayscale
            lbl = np.array(Image.open(lbl_path))

            volume_img.append(img)
            volume_lbl.append(lbl)

        # Convertir en arrays numpy
        vol_img_np = np.stack(volume_img, axis=0)
        vol_lbl_np = np.stack(volume_lbl, axis=0)

        # # Transposer pour avoir la forme (640, 440, 30) au lieu de (30, 640, 440)
        # vol_img_np = np.transpose(vol_img_np, (2, 1, 0))
        # vol_lbl_np = np.transpose(vol_lbl_np, (2, 1, 0))

        # Nom du volume
        vol_name = f"volume_{vol_idx+1:03d}"

        # Sauvegarder les volumes NIfTI
        nib.save(nib.Nifti1Image(vol_img_np, affine=np.eye(4)),
                imagesTr_dir / f"{vol_name}_0000{file_ending}")
        nib.save(nib.Nifti1Image(vol_lbl_np.astype(np.uint8), affine=np.eye(4)),
                labelsTr_dir / f"{vol_name}{file_ending}")

        print(f"✅ Volume {vol_name} créé: {end_idx - start_idx} slices")

    print(f"📦 Total volumes créés: {num_volumes}")

    # === Génération du dataset.json ===
    dataset_json = {
        "channel_names": {
            "0": "ultrasound"
        },
        "labels": {
            "background": 0,
            "frontwall": 1,
            "backwall": 2,
            "flaw": 3,
            "indication": 4
        },
        "numTraining": num_volumes,
        "file_ending": file_ending,
        "overwrite_image_reader_writer": "SimpleITKIO"
    }

    with open(output_dir / "dataset.json", "w") as f:
        json.dump(dataset_json, f, indent=4)

    print(f"\n📄 dataset.json créé dans : {output_dir}")
    print(f"📦 Total volumes convertis : {num_volumes}")
    return True


# ========================================
# CONFIGURATION - MODIFIEZ VOS CHEMINS ICI
# ========================================

# Vos chemins de données
IMAGES_ROOT = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset014_test4labeldifferentUint8\imagesTr"
LABELS_ROOT = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset014_test4labeldifferentUint8\labelsTr"
OUTPUT_DIR = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw"

# Configuration du dataset
DATASET_NAME = "Dataset015_test4labeldifferentUint8_3d"
FILE_ENDING = ".nii.gz"
SLICES_PER_VOLUME = 30  # Nombre de slices 2D par volume 3D


if __name__ == "__main__":
    print("🎯 CONVERSION DATASET 2D VERS 3D POUR nnU-Net")
    print("=" * 50)
    print(f"📂 Images: {IMAGES_ROOT}")
    print(f"🏷️  Labels: {LABELS_ROOT}")
    print(f"📤 Output: {OUTPUT_DIR}")
    print(f"📋 Dataset: {DATASET_NAME}")
    print(f"🔢 Slices par volume: {SLICES_PER_VOLUME}")
    print("=" * 50)

    # Vérification des chemins
    if not Path(IMAGES_ROOT).exists():
        print(f"❌ ERREUR: Dossier images introuvable: {IMAGES_ROOT}")
        exit(1)

    if not Path(LABELS_ROOT).exists():
        print(f"❌ ERREUR: Dossier labels introuvable: {LABELS_ROOT}")
        exit(1)

    # Lancement de la conversion
    success = convert_png_to_nifti_3d(
        images_root=IMAGES_ROOT,
        labels_root=LABELS_ROOT,
        output_dataset_dir=OUTPUT_DIR,
        dataset_name=DATASET_NAME,
        file_ending=FILE_ENDING,
        slices_per_volume=SLICES_PER_VOLUME
    )

    if success:
        print("\n🎉 Conversion terminée avec succès!")
    else:
        print("\n💥 Échec de la conversion!")
