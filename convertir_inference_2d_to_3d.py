"""
Script pour convertir des PNG 2D (images) en volumes 3D NIfTI pour l'inférence avec nnU-Net.

Auteur : Gabriel Forest
Date   : 2025-06-16
"""

import os
import numpy as np
import nibabel as nib
from PIL import Image
from pathlib import Path
from natsort import natsorted


def convert_png_to_nifti_volumes_inference(images_root, output_imagesTs,
                                           file_ending=".nii.gz", slices_per_volume=10):
    """
    Convertit des images PNG 2D en volumes 3D NIfTI pour l'inférence nnU-Net.

    Args:
        images_root (str): Dossier contenant les images PNG
        output_imagesTs (str): Dossier de sortie `imagesTs`
        file_ending (str): Extension de fichier (par défaut .nii.gz)
        slices_per_volume (int): Nombre de slices par volume
    """
    images_root = Path(images_root)
    output_imagesTs = Path(output_imagesTs)
    output_imagesTs.mkdir(parents=True, exist_ok=True)

    # Récupérer et trier les PNG
    img_files = natsorted([f for f in os.listdir(images_root) if f.lower().endswith(".png")])
    if len(img_files) == 0:
        print("❌ Aucune image PNG trouvée.")
        return False

    print(f"📂 {len(img_files)} PNG trouvés dans {images_root}")

    # Découpage en volumes
    num_volumes = len(img_files) // slices_per_volume
    if len(img_files) % slices_per_volume != 0:
        num_volumes += 1  # inclure les restants

    print(f"🔄 Conversion en {num_volumes} volume(s) de {slices_per_volume} slices max")

    for vol_idx in range(num_volumes):
        start_idx = vol_idx * slices_per_volume
        end_idx = min(start_idx + slices_per_volume, len(img_files))

        volume_img = []
        for i in range(start_idx, end_idx):
            img_path = images_root / img_files[i]
            img = np.array(Image.open(img_path).convert("L"))
            volume_img.append(img)

        vol_img_np = np.stack(volume_img, axis=0)
        vol_name = f"case_{vol_idx+1:03d}"
        output_path = output_imagesTs / f"{vol_name}_0000{file_ending}"

        nib.save(nib.Nifti1Image(vol_img_np, affine=np.eye(4)), output_path)
        print(f"✅ {output_path.name} créé avec {vol_img_np.shape[0]} slices")

    print(f"\n🎯 Tous les volumes ont été générés dans : {output_imagesTs}")
    return True


# ========================
# CONFIGURATION UTILISATEUR
# ========================

IMAGES_ROOT = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent"
OUTPUT_IMAGESTS = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent_3d"
SLICES_PER_VOLUME = 30  # Modifie si tu veux grouper autrement
FILE_ENDING = ".nii.gz"


if __name__ == "__main__":
    print("🔁 CONVERSION POUR INFÉRENCE nnU-Net")
    print("=" * 50)
    print(f"📂 Images source       : {IMAGES_ROOT}")
    print(f"📤 Dossier output      : {OUTPUT_IMAGESTS}")
    print(f"🔢 Slices par volume   : {SLICES_PER_VOLUME}")
    print("=" * 50)

    success = convert_png_to_nifti_volumes_inference(
        images_root=IMAGES_ROOT,
        output_imagesTs=OUTPUT_IMAGESTS,
        file_ending=FILE_ENDING,
        slices_per_volume=SLICES_PER_VOLUME
    )

    if success:
        print("🎉 Conversion terminée avec succès.")
    else:
        print("💥 Échec de la conversion.")
