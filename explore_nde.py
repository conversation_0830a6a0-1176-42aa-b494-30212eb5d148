"""
Script pour explorer la structure d'un fichier .nde (HDF5)
Auteur: Gabriel Forest
Date: 2025-06-18
"""

import h5py
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

def explore_nde_structure(file_path):
    """Explore la structure hiérarchique d'un fichier .nde"""
    print(f"Exploration du fichier: {file_path}")
    print("=" * 60)
    
    with h5py.File(file_path, 'r') as f:
        def print_structure(name, obj):
            indent = "  " * name.count('/')
            if isinstance(obj, h5py.Group):
                print(f"{indent}📁 Groupe: {name}")
            elif isinstance(obj, h5py.Dataset):
                print(f"{indent}📄 Dataset: {name}")
                print(f"{indent}   - Shape: {obj.shape}")
                print(f"{indent}   - Dtype: {obj.dtype}")
                print(f"{indent}   - Size: {obj.size} éléments")
                
                # Afficher quelques valeurs pour les petits datasets
                if obj.size < 100:
                    print(f"{indent}   - Valeurs: {obj[...]}")
                elif len(obj.shape) >= 2:
                    print(f"{indent}   - Min: {np.min(obj)}, Max: {np.max(obj)}")
                    print(f"{indent}   - Moyenne: {np.mean(obj):.2f}")
        
        f.visititems(print_structure)

def find_image_datasets(file_path):
    """Trouve tous les datasets qui ressemblent à des images"""
    image_datasets = []
    
    with h5py.File(file_path, 'r') as f:
        def check_dataset(name, obj):
            if isinstance(obj, h5py.Dataset):
                # Critères pour identifier une image:
                # - Au moins 2 dimensions
                # - Taille raisonnable pour une image
                if len(obj.shape) >= 2 and obj.size > 1000:
                    image_datasets.append({
                        'name': name,
                        'shape': obj.shape,
                        'dtype': obj.dtype,
                        'size': obj.size
                    })
        
        f.visititems(check_dataset)
    
    return image_datasets

def preview_images(file_path, max_images=5):
    """Affiche un aperçu des images trouvées"""
    image_datasets = find_image_datasets(file_path)
    
    if not image_datasets:
        print("Aucun dataset d'image trouvé!")
        return
    
    print(f"\nDatasets d'images trouvés: {len(image_datasets)}")
    print("-" * 40)
    
    for i, dataset_info in enumerate(image_datasets[:max_images]):
        print(f"\n{i+1}. {dataset_info['name']}")
        print(f"   Shape: {dataset_info['shape']}")
        print(f"   Dtype: {dataset_info['dtype']}")
        
        # Essayer de charger et afficher l'image
        try:
            with h5py.File(file_path, 'r') as f:
                data = f[dataset_info['name']][...]
                
                # Si c'est un array 3D, prendre la première slice
                if len(data.shape) == 3:
                    img = data[0] if data.shape[0] < data.shape[2] else data[:,:,0]
                elif len(data.shape) == 2:
                    img = data
                else:
                    print(f"   Format non supporté pour l'affichage")
                    continue
                
                print(f"   Image shape: {img.shape}")
                print(f"   Min: {np.min(img)}, Max: {np.max(img)}")
                print(f"   Dtype: {img.dtype}")
                
                # Identifier le type d'image
                if img.dtype == np.uint8:
                    print(f"   Type: Image uint8 (raw)")
                elif img.dtype in [np.float32, np.float64]:
                    print(f"   Type: Image float (possiblement RGB normalisée)")
                
        except Exception as e:
            print(f"   Erreur lors du chargement: {e}")

if __name__ == "__main__":
    nde_file = Path("nde/test.nde")
    
    if not nde_file.exists():
        print(f"Fichier non trouvé: {nde_file}")
        exit(1)
    
    # Explorer la structure
    explore_nde_structure(nde_file)
    
    print("\n" + "=" * 60)
    
    # Prévisualiser les images
    preview_images(nde_file)
