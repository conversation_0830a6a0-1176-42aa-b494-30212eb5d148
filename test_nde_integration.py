"""
Script de test pour vérifier l'intégration du visualiseur NDE
Auteur: Gabriel Forest
Date: 2025-06-18
"""

import sys
import os
from pathlib import Path

def test_nde_viewer():
    """Test du visualiseur NDE"""
    print("Test du visualiseur NDE...")
    
    # Vérifier que le fichier NDE existe
    nde_file = Path("nde/test.nde")
    if not nde_file.exists():
        print(f"❌ Fichier NDE non trouvé: {nde_file}")
        return False
    
    print(f"✅ Fichier NDE trouvé: {nde_file}")
    
    # Tester l'import du module
    try:
        from view_nde_files import NDEViewer
        print("✅ Module view_nde_files importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    
    # Tester la création du viewer (sans affichage)
    try:
        viewer = NDEViewer(nde_file)
        print("✅ NDEViewer créé avec succès")
        print(f"📊 Datasets trouvés: {len(viewer.datasets)}")
        for i, ds in enumerate(viewer.datasets):
            print(f"  {i}: {ds['name']} - Shape: {ds['shape']} - Type: {ds['dtype']}")
        return True
    except Exception as e:
        print(f"❌ Erreur lors de la création du viewer: {e}")
        return False

def test_interface_integration():
    """Test de l'intégration dans l'interface"""
    print("\nTest de l'intégration dans l'interface...")
    
    try:
        from interface_graphique import ScriptGUI
        print("✅ Module interface_graphique importé avec succès")
        
        # Vérifier que le script NDE est dans la liste
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Cacher la fenêtre
        
        gui = ScriptGUI(root)
        scripts = gui.script_combo['values']
        
        nde_script_found = any("view_nde_files" in script for script in scripts)
        if nde_script_found:
            print("✅ Script NDE trouvé dans l'interface")
        else:
            print("❌ Script NDE non trouvé dans l'interface")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'intégration: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Test d'intégration du visualiseur NDE")
    print("=" * 50)
    
    success1 = test_nde_viewer()
    success2 = test_interface_integration()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 Tous les tests sont passés avec succès!")
        print("\n📋 Instructions d'utilisation:")
        print("1. Lancez l'interface graphique: python interface_graphique.py")
        print("2. Sélectionnez '🔬 Visualiser fichiers NDE (view_nde_files.py)'")
        print("3. Choisissez votre fichier .nde")
        print("4. Cliquez sur 'Exécuter'")
    else:
        print("❌ Certains tests ont échoué")
        sys.exit(1)
