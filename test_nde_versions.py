"""
Script de test pour comparer les versions du visualiseur NDE
Auteur: Gabriel <PERSON>
Date: 2025-06-18
"""

import sys
from pathlib import Path

def test_version_comparison():
    """Compare les deux versions du visualiseur"""
    print("🧪 Test de comparaison des versions du visualiseur NDE")
    print("=" * 60)
    
    # Vérifier que le fichier NDE existe
    nde_file = Path("nde/test.nde")
    if not nde_file.exists():
        print(f"❌ Fichier NDE non trouvé: {nde_file}")
        return False
    
    print(f"✅ Fichier NDE trouvé: {nde_file}")
    
    # Test version originale
    print("\n📋 Test de la version originale...")
    try:
        from view_nde_files import NDEViewer
        viewer1 = NDEViewer(nde_file)
        print("✅ Version originale : OK")
        print(f"   Datasets: {len(viewer1.datasets)}")
    except Exception as e:
        print(f"❌ Version originale : Erreur - {e}")
        return False
    
    # Test version stable
    print("\n📋 Test de la version stable...")
    try:
        from view_nde_files_stable import NDEViewerStable
        viewer2 = NDEViewerStable(nde_file)
        print("✅ Version stable : OK")
        print(f"   Datasets: {len(viewer2.datasets)}")
    except Exception as e:
        print(f"❌ Version stable : Erreur - {e}")
        return False
    
    # Comparaison des datasets
    print("\n📊 Comparaison des datasets détectés...")
    if len(viewer1.datasets) == len(viewer2.datasets):
        print("✅ Même nombre de datasets détectés")
        for i, (ds1, ds2) in enumerate(zip(viewer1.datasets, viewer2.datasets)):
            if ds1['name'] == ds2['name'] and ds1['shape'] == ds2['shape']:
                print(f"   ✅ Dataset {i}: {ds1['name']} - Identique")
            else:
                print(f"   ❌ Dataset {i}: Différence détectée")
                return False
    else:
        print(f"❌ Nombre de datasets différent: {len(viewer1.datasets)} vs {len(viewer2.datasets)}")
        return False
    
    return True

def show_usage_instructions():
    """Affiche les instructions d'utilisation"""
    print("\n" + "=" * 60)
    print("📋 INSTRUCTIONS D'UTILISATION")
    print("=" * 60)
    
    print("\n🔧 PROBLÈME RÉSOLU:")
    print("   Le problème de rétrécissement des images lors du changement")
    print("   de slice a été corrigé dans la version stable.")
    
    print("\n🚀 UTILISATION RECOMMANDÉE:")
    print("   1. Via l'interface graphique (mise à jour automatiquement):")
    print("      python interface_graphique.py")
    print("      → Sélectionner '🔬 Visualiser fichiers NDE'")
    
    print("\n   2. En ligne de commande (version stable):")
    print("      python view_nde_files_stable.py nde/test.nde")
    
    print("\n   3. En ligne de commande (version originale corrigée):")
    print("      python view_nde_files.py nde/test.nde")
    
    print("\n🎮 CONTRÔLES:")
    print("   ← → (ou A/D) : Navigation entre slices")
    print("   ↑ ↓ (ou W/S) : Navigation entre datasets")
    print("   Home/End     : Première/dernière slice")
    print("   I            : Informations détaillées")
    print("   Q/Escape     : Quitter")
    print("   + Boutons de navigation (version stable)")
    
    print("\n🔍 AMÉLIORATIONS VERSION STABLE:")
    print("   ✅ Axes fixes - pas de redimensionnement")
    print("   ✅ Colorbar dédiée - pas de conflit")
    print("   ✅ Boutons de navigation intégrés")
    print("   ✅ Interface plus robuste")
    print("   ✅ Gestion d'erreur améliorée")

if __name__ == "__main__":
    success = test_version_comparison()
    
    if success:
        print("\n🎉 Tous les tests sont passés avec succès!")
        show_usage_instructions()
    else:
        print("\n❌ Certains tests ont échoué")
        sys.exit(1)
