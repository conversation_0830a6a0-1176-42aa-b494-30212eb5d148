"""
Script de test pour vérifier la correction des dimensions des slices
Auteur: Gabriel Forest
Date: 2025-06-18
"""

import sys
from pathlib import Path

def test_slice_dimensions():
    """Test de la correction des dimensions"""
    print("🧪 Test de la correction des dimensions des slices")
    print("=" * 60)
    
    # Vérifier que le fichier NDE existe
    nde_file = Path("nde/test.nde")
    if not nde_file.exists():
        print(f"❌ Fichier NDE non trouvé: {nde_file}")
        return False
    
    print(f"✅ Fichier NDE trouvé: {nde_file}")
    
    # Test avec la version corrigée
    print("\n📋 Test avec la version stable corrigée...")
    try:
        from view_nde_files_stable import NDEViewerStable
        viewer = NDEViewerStable(nde_file)
        
        print(f"\n📊 Analyse des datasets:")
        for i, ds in enumerate(viewer.datasets):
            print(f"   Dataset {i}: {ds['name']}")
            print(f"   Shape originale: {ds['shape']}")
            print(f"   Type: {ds['dtype']}")
            print(f"   Est 3D: {ds['is_3d']}")
            print(f"   Nombre de slices: {ds['num_slices']}")
            
            if ds['is_3d']:
                print(f"   → Interprétation: {ds['num_slices']} images de {ds['shape'][1]}x{ds['shape'][2]}")
            else:
                print(f"   → Interprétation: 1 image de {ds['shape'][0]}x{ds['shape'][1]}")
            print()
        
        # Test de navigation sur le dataset 3D
        amplitude_dataset = None
        for i, ds in enumerate(viewer.datasets):
            if ds['is_3d']:
                amplitude_dataset = i
                break
        
        if amplitude_dataset is not None:
            print(f"🎯 Test de navigation sur le dataset 3D (index {amplitude_dataset}):")
            viewer.current_dataset = amplitude_dataset
            viewer._load_current_data()
            
            # Tester quelques slices
            test_slices = [0, 1, 100, 500, 1049]  # 1049 = dernière slice (1050-1)
            
            for slice_idx in test_slices:
                if slice_idx < viewer.datasets[amplitude_dataset]['num_slices']:
                    viewer.current_slice = slice_idx
                    img = viewer._get_current_image()
                    print(f"   Slice {slice_idx+1}/1050: Shape = {img.shape} ✅")
                else:
                    print(f"   Slice {slice_idx+1}: Hors limites ❌")
            
            print(f"\n✅ Navigation corrigée - Maintenant 1050 slices de 55x640 !")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_correction_summary():
    """Affiche le résumé de la correction"""
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DE LA CORRECTION")
    print("=" * 60)
    
    print("\n🔧 PROBLÈME IDENTIFIÉ:")
    print("   Shape du dataset 'Amplitude': (1050, 55, 640)")
    print("   ❌ Ancienne interprétation: 640 slices de 1050x55")
    print("   ✅ Nouvelle interprétation: 1050 slices de 55x640")
    
    print("\n🛠️ CORRECTIONS APPORTÉES:")
    print("   1. num_slices = obj.shape[0] au lieu de obj.shape[2]")
    print("   2. img = data[slice_idx, :, :] au lieu de data[:, :, slice_idx]")
    print("   3. Mise à jour des deux versions (stable et originale)")
    
    print("\n✅ RÉSULTAT:")
    print("   • Dataset 'Amplitude': 1050 slices navigables (uint16)")
    print("   • Dataset 'Status': 1 image fixe (uint8)")
    print("   • Navigation ← → fonctionne maintenant sur les 1050 slices!")
    
    print("\n🎮 NAVIGATION MISE À JOUR:")
    print("   • ← → : Navigue entre les slices 1/1050 → 2/1050 → ... → 1050/1050")
    print("   • ↑ ↓ : Change de dataset (Amplitude ↔ Status)")
    print("   • Home/End : Première/dernière slice")
    
    print("\n🚀 POUR TESTER:")
    print("   python view_nde_files_stable.py")
    print("   → Vous devriez maintenant pouvoir naviguer entre 1050 slices!")

if __name__ == "__main__":
    success = test_slice_dimensions()
    
    if success:
        show_correction_summary()
        print("\n🎉 Correction réussie ! Le visualiseur fonctionne maintenant correctement.")
    else:
        print("\n❌ Erreur lors du test de correction")
        sys.exit(1)
