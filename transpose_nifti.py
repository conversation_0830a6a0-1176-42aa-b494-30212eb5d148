import nibabel as nib
import numpy as np

def transpose_nifti(input_path, output_path):
    try:
        # Charger le fichier NIfTI
        nifti_img = nib.load(input_path)
        
        # Obtenir les données et l'affine
        data = nifti_img.get_fdata()
        affine = nifti_img.affine
        
        # Transposer les données (20, 640, 440) -> (640, 440, 20)
        transposed_data = np.transpose(data, (1, 2, 0))
        
        # Créer une nouvelle image NIfTI avec les données transposées
        new_nifti = nib.Nifti1Image(transposed_data, affine)
        
        # Sauvegarder la nouvelle image
        nib.save(new_nifti, output_path)
        
        print(f"Image originale - Forme: {data.shape}")
        print(f"Image transposée - Forme: {transposed_data.shape}")
        print(f"Image sauvegardée dans: {output_path}")
        
    except Exception as e:
        print(f"Erreur lors du traitement: {str(e)}")

if __name__ == "__main__":
    # Chemins des fichiers
    input_path = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent_3d\case_001_0000.nii.gz"
    output_path = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent_3d\case_001_0000_transposed.nii.gz"
    
    transpose_nifti(input_path, output_path) 