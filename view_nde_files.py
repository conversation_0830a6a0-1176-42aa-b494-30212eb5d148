"""
Visualiseur interactif pour fichiers .nde (HDF5)
Auteur: Gabriel Forest
Date: 2025-06-18

Ce script permet de visualiser les images contenues dans les fichiers .nde
avec navigation interactive et contrôles clavier.
"""

import h5py
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
import sys

class NDEViewer:
    def __init__(self, file_path):
        """
        Initialise le visualiseur pour un fichier .nde
        
        Args:
            file_path (str): Chemin vers le fichier .nde
        """
        self.file_path = Path(file_path)
        self.current_slice = 0
        self.current_dataset = 0
        self.datasets = []
        self.data_cache = {}
        self.cbar_ax = None
        self._first_display = False
        
        # Vérifier que le fichier existe
        if not self.file_path.exists():
            raise FileNotFoundError(f"Fichier non trouvé: {self.file_path}")
        
        # Charger les informations sur les datasets
        self._load_datasets_info()
        
        if not self.datasets:
            raise ValueError("Aucun dataset d'image trouvé dans le fichier .nde")
        
        # Initialiser l'interface
        self._setup_interface()
        self._load_current_data()
        self._update_display()
        
    def _load_datasets_info(self):
        """Charge les informations sur tous les datasets d'images disponibles"""
        with h5py.File(self.file_path, 'r') as f:
            def find_datasets(name, obj):
                if isinstance(obj, h5py.Dataset):
                    # Critères pour identifier une image:
                    # - Au moins 2 dimensions
                    # - Taille raisonnable
                    if len(obj.shape) >= 2 and obj.size > 1000:
                        self.datasets.append({
                            'name': name,
                            'path': name,
                            'shape': obj.shape,
                            'dtype': obj.dtype,
                            'is_3d': len(obj.shape) == 3,
                            'num_slices': obj.shape[0] if len(obj.shape) == 3 else 1
                        })
            
            f.visititems(find_datasets)
        
        # Trier par nom pour un ordre cohérent
        self.datasets.sort(key=lambda x: x['name'])
        
        print(f"Datasets trouvés:")
        for i, ds in enumerate(self.datasets):
            print(f"  {i}: {ds['name']} - Shape: {ds['shape']} - Type: {ds['dtype']}")
    
    def _setup_interface(self):
        """Configure l'interface matplotlib"""
        self.fig, self.ax = plt.subplots(figsize=(14, 10))
        self.fig.suptitle(f"Visualiseur NDE - {self.file_path.name}", fontsize=14)

        # Ajuster les marges pour faire de la place à la colorbar
        plt.subplots_adjust(left=0.1, right=0.85, top=0.9, bottom=0.1)

        # Initialiser la colorbar à None
        self.colorbar = None

        # Connecter les événements clavier
        self.fig.canvas.mpl_connect('key_press_event', self._on_key_press)

        # Afficher les contrôles
        self._show_controls()
    

    
    def _load_current_data(self):
        """Charge les données du dataset actuel"""
        dataset_info = self.datasets[self.current_dataset]
        dataset_path = dataset_info['path']
        
        # Utiliser le cache si disponible
        if dataset_path not in self.data_cache:
            print(f"Chargement du dataset: {dataset_path}")
            with h5py.File(self.file_path, 'r') as f:
                self.data_cache[dataset_path] = f[dataset_path][...]
        
        self.current_data = self.data_cache[dataset_path]
        
        # Ajuster l'index de slice si nécessaire
        if dataset_info['is_3d']:
            max_slice = dataset_info['num_slices'] - 1
            self.current_slice = min(self.current_slice, max_slice)
        else:
            self.current_slice = 0
    
    def _get_current_image(self):
        """Récupère l'image actuelle à afficher"""
        dataset_info = self.datasets[self.current_dataset]

        if dataset_info['is_3d']:
            # Pour les données 3D, extraire la slice actuelle
            # Shape est (num_slices, height, width) donc slice = index 0
            if self.current_slice < self.current_data.shape[0]:
                img = self.current_data[self.current_slice, :, :]
            else:
                img = self.current_data[0, :, :]
        else:
            # Pour les données 2D
            img = self.current_data

        return img
    
    def _update_display(self):
        """Met à jour l'affichage"""
        # Sauvegarder les limites actuelles si elles existent
        xlim = None
        ylim = None
        if hasattr(self, '_first_display') and self._first_display:
            try:
                xlim = self.ax.get_xlim()
                ylim = self.ax.get_ylim()
            except:
                pass

        # Effacer seulement le contenu, pas les axes
        self.ax.clear()

        # Récupérer l'image actuelle
        img = self._get_current_image()
        dataset_info = self.datasets[self.current_dataset]

        # Déterminer la colormap appropriée
        if dataset_info['dtype'] == np.uint8 and np.max(img) <= 1:
            # Masque binaire
            cmap = 'gray'
            vmin, vmax = 0, 1
        elif dataset_info['dtype'] == np.uint8:
            # Image uint8 standard
            cmap = 'gray'
            vmin, vmax = 0, 255
        else:
            # Données d'amplitude ou autres
            cmap = 'viridis'
            vmin, vmax = np.min(img), np.max(img)

        # Afficher l'image avec aspect auto pour éviter les déformations
        im = self.ax.imshow(img, cmap=cmap, vmin=vmin, vmax=vmax, aspect='auto', interpolation='nearest')

        # Gérer la colorbar de manière plus stable
        if self.colorbar is not None:
            try:
                self.colorbar.remove()
            except:
                pass
            self.colorbar = None

        # Créer une nouvelle colorbar dans un espace dédié
        if not hasattr(self, 'cbar_ax') or self.cbar_ax is None:
            # Créer un axe dédié pour la colorbar
            self.cbar_ax = self.fig.add_axes([0.87, 0.1, 0.03, 0.8])
        else:
            self.cbar_ax.clear()

        self.colorbar = self.fig.colorbar(im, cax=self.cbar_ax)

        # Titre avec informations
        title = f"{dataset_info['name']}"
        if dataset_info['is_3d']:
            title += f" - Slice {self.current_slice+1}/{dataset_info['num_slices']} (3D)"
        else:
            title += f" (2D - pas de slices)"
        title += f"\nShape: {img.shape} | Type: {dataset_info['dtype']} | Min: {np.min(img)} | Max: {np.max(img)}"

        self.ax.set_title(title, fontsize=10)
        self.ax.set_xlabel('X')
        self.ax.set_ylabel('Y')

        # Restaurer les limites si c'était un changement de slice (pas de dataset)
        if xlim is not None and ylim is not None and hasattr(self, '_last_dataset') and self._last_dataset == self.current_dataset:
            try:
                self.ax.set_xlim(xlim)
                self.ax.set_ylim(ylim)
            except:
                pass

        # Mémoriser le dataset actuel et marquer comme affiché
        self._last_dataset = self.current_dataset
        self._first_display = True

        # Forcer le redraw
        self.fig.canvas.draw_idle()
    

    
    def _on_key_press(self, event):
        """Gestion des événements clavier"""
        dataset_info = self.datasets[self.current_dataset]
        
        if event.key == 'right' or event.key == 'd':
            # Slice suivante
            if not dataset_info['is_3d']:
                print(f"ℹ️ Dataset '{dataset_info['name']}' est 2D - pas de slices à naviguer")
            elif self.current_slice < dataset_info['num_slices'] - 1:
                self.current_slice += 1
                self._update_display()
                print(f"➡️ Slice {self.current_slice+1}/{dataset_info['num_slices']}")
            else:
                print(f"ℹ️ Déjà à la dernière slice ({dataset_info['num_slices']}/{dataset_info['num_slices']})")

        elif event.key == 'left' or event.key == 'a':
            # Slice précédente
            if not dataset_info['is_3d']:
                print(f"ℹ️ Dataset '{dataset_info['name']}' est 2D - pas de slices à naviguer")
            elif self.current_slice > 0:
                self.current_slice -= 1
                self._update_display()
                print(f"⬅️ Slice {self.current_slice+1}/{dataset_info['num_slices']}")
            else:
                print(f"ℹ️ Déjà à la première slice (1/{dataset_info['num_slices']})")
        
        elif event.key == 'up' or event.key == 'w':
            # Dataset suivant
            if self.current_dataset < len(self.datasets) - 1:
                self.current_dataset += 1
                self._load_current_data()
                self._update_display()

        elif event.key == 'down' or event.key == 's':
            # Dataset précédent
            if self.current_dataset > 0:
                self.current_dataset -= 1
                self._load_current_data()
                self._update_display()
        
        elif event.key == 'home':
            # Première slice
            if dataset_info['is_3d']:
                self.current_slice = 0
                self._update_display()
        
        elif event.key == 'end':
            # Dernière slice
            if dataset_info['is_3d']:
                self.current_slice = dataset_info['num_slices'] - 1
                self._update_display()
        
        elif event.key == 'i':
            # Informations détaillées
            self._show_info()
        
        elif event.key == 'h':
            # Aide
            self._show_controls()
        
        elif event.key == 'q' or event.key == 'escape':
            # Quitter
            plt.close()
    
    def _show_controls(self):
        """Affiche les contrôles disponibles"""
        controls = """
        CONTRÔLES DISPONIBLES:
        ← → (ou A/D) : Slice précédente/suivante
        ↑ ↓ (ou W/S) : Dataset précédent/suivant
        Home/End     : Première/dernière slice
        I            : Informations détaillées
        H            : Afficher cette aide
        Q/Escape     : Quitter
        """
        print(controls)
    
    def _show_info(self):
        """Affiche des informations détaillées sur l'image actuelle"""
        img = self._get_current_image()
        dataset_info = self.datasets[self.current_dataset]
        
        info = f"""
        INFORMATIONS DÉTAILLÉES:
        Fichier: {self.file_path}
        Dataset: {dataset_info['name']}
        Shape complète: {dataset_info['shape']}
        Type de données: {dataset_info['dtype']}
        Image actuelle: {img.shape}
        Valeurs: Min={np.min(img)}, Max={np.max(img)}, Moyenne={np.mean(img):.2f}
        """
        if dataset_info['is_3d']:
            info += f"Slice: {self.current_slice+1}/{dataset_info['num_slices']}"
        
        print(info)
    
    def show(self):
        """Affiche le visualiseur"""
        print(f"Ouverture du visualiseur pour: {self.file_path}")
        self._show_controls()
        plt.show()


def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description="Visualiseur pour fichiers .nde")
    parser.add_argument("file_path", nargs='?', default="nde/test.nde",
                       help="Chemin vers le fichier .nde")
    
    args = parser.parse_args()
    
    try:
        viewer = NDEViewer(args.file_path)
        viewer.show()
    except Exception as e:
        print(f"Erreur: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
